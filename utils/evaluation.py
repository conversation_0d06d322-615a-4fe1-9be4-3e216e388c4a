"""
Model Evaluation and Visualization Utilities for ASL Detection
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    classification_report, confusion_matrix, 
    precision_recall_curve, roc_curve, auc
)
import tensorflow as tf
import json
import os
from datetime import datetime

class ModelEvaluator:
    def __init__(self, model_path, class_names):
        """
        Initialize Model Evaluator
        
        Args:
            model_path (str): Path to the trained model
            class_names (list): List of class names
        """
        self.model_path = model_path
        self.class_names = class_names
        self.model = None
        self.load_model()
        
    def load_model(self):
        """Load the trained model"""
        try:
            self.model = tf.keras.models.load_model(self.model_path)
            print(f"Model loaded successfully from {self.model_path}")
        except Exception as e:
            print(f"Error loading model: {e}")
            
    def evaluate_on_test_set(self, X_test, y_test, save_results=True):
        """
        Comprehensive evaluation on test set
        
        Args:
            X_test (np.array): Test images
            y_test (np.array): Test labels (one-hot encoded)
            save_results (bool): Whether to save results to files
            
        Returns:
            dict: Evaluation results
        """
        if self.model is None:
            raise ValueError("Model not loaded")
            
        print("Evaluating model on test set...")
        
        # Get predictions
        y_pred_proba = self.model.predict(X_test, verbose=1)
        y_pred = np.argmax(y_pred_proba, axis=1)
        y_true = np.argmax(y_test, axis=1)
        
        # Calculate metrics
        test_loss, test_accuracy = self.model.evaluate(X_test, y_test, verbose=0)[:2]
        
        # Classification report
        report = classification_report(
            y_true, y_pred, 
            target_names=self.class_names,
            output_dict=True
        )
        
        # Confusion matrix
        cm = confusion_matrix(y_true, y_pred)
        
        # Per-class accuracy
        per_class_accuracy = cm.diagonal() / cm.sum(axis=1)
        
        results = {
            'test_loss': test_loss,
            'test_accuracy': test_accuracy,
            'classification_report': report,
            'confusion_matrix': cm,
            'per_class_accuracy': per_class_accuracy,
            'y_true': y_true,
            'y_pred': y_pred,
            'y_pred_proba': y_pred_proba
        }
        
        if save_results:
            self.save_evaluation_results(results)
            
        return results
    
    def plot_confusion_matrix(self, cm, save_path='logs/confusion_matrix_detailed.png'):
        """
        Plot detailed confusion matrix
        
        Args:
            cm (np.array): Confusion matrix
            save_path (str): Path to save the plot
        """
        plt.figure(figsize=(15, 12))
        
        # Normalize confusion matrix
        cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        
        # Create heatmap
        sns.heatmap(cm_normalized, 
                   annot=True, 
                   fmt='.2f', 
                   cmap='Blues',
                   xticklabels=self.class_names, 
                   yticklabels=self.class_names,
                   cbar_kws={'label': 'Normalized Count'})
        
        plt.title('Normalized Confusion Matrix', fontsize=16)
        plt.xlabel('Predicted Label', fontsize=14)
        plt.ylabel('True Label', fontsize=14)
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Confusion matrix saved to {save_path}")
        
        plt.show()
    
    def plot_per_class_metrics(self, report, save_path='logs/per_class_metrics.png'):
        """
        Plot per-class precision, recall, and F1-score
        
        Args:
            report (dict): Classification report dictionary
            save_path (str): Path to save the plot
        """
        # Extract per-class metrics
        classes = [cls for cls in report.keys() if cls not in ['accuracy', 'macro avg', 'weighted avg']]
        
        precision = [report[cls]['precision'] for cls in classes]
        recall = [report[cls]['recall'] for cls in classes]
        f1_score = [report[cls]['f1-score'] for cls in classes]
        
        # Create DataFrame
        metrics_df = pd.DataFrame({
            'Class': classes,
            'Precision': precision,
            'Recall': recall,
            'F1-Score': f1_score
        })
        
        # Plot
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))
        
        # Precision
        axes[0].bar(metrics_df['Class'], metrics_df['Precision'], color='skyblue', alpha=0.7)
        axes[0].set_title('Precision per Class', fontsize=14)
        axes[0].set_ylabel('Precision', fontsize=12)
        axes[0].tick_params(axis='x', rotation=45)
        axes[0].grid(True, alpha=0.3)
        
        # Recall
        axes[1].bar(metrics_df['Class'], metrics_df['Recall'], color='lightgreen', alpha=0.7)
        axes[1].set_title('Recall per Class', fontsize=14)
        axes[1].set_ylabel('Recall', fontsize=12)
        axes[1].tick_params(axis='x', rotation=45)
        axes[1].grid(True, alpha=0.3)
        
        # F1-Score
        axes[2].bar(metrics_df['Class'], metrics_df['F1-Score'], color='lightcoral', alpha=0.7)
        axes[2].set_title('F1-Score per Class', fontsize=14)
        axes[2].set_ylabel('F1-Score', fontsize=12)
        axes[2].set_xlabel('Class', fontsize=12)
        axes[2].tick_params(axis='x', rotation=45)
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Per-class metrics plot saved to {save_path}")
        
        plt.show()
        
        return metrics_df
    
    def plot_prediction_confidence_distribution(self, y_pred_proba, save_path='logs/confidence_distribution.png'):
        """
        Plot distribution of prediction confidences
        
        Args:
            y_pred_proba (np.array): Prediction probabilities
            save_path (str): Path to save the plot
        """
        # Get max confidence for each prediction
        max_confidences = np.max(y_pred_proba, axis=1)
        
        plt.figure(figsize=(12, 6))
        
        # Histogram of confidences
        plt.subplot(1, 2, 1)
        plt.hist(max_confidences, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        plt.title('Distribution of Prediction Confidences')
        plt.xlabel('Maximum Confidence')
        plt.ylabel('Frequency')
        plt.grid(True, alpha=0.3)
        
        # Box plot
        plt.subplot(1, 2, 2)
        plt.boxplot(max_confidences, vert=True)
        plt.title('Confidence Distribution Box Plot')
        plt.ylabel('Maximum Confidence')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Confidence distribution plot saved to {save_path}")
        
        plt.show()
        
        # Print statistics
        print(f"Confidence Statistics:")
        print(f"Mean: {np.mean(max_confidences):.4f}")
        print(f"Median: {np.median(max_confidences):.4f}")
        print(f"Std: {np.std(max_confidences):.4f}")
        print(f"Min: {np.min(max_confidences):.4f}")
        print(f"Max: {np.max(max_confidences):.4f}")
    
    def analyze_misclassifications(self, X_test, y_true, y_pred, y_pred_proba, 
                                 num_examples=10, save_path='logs/misclassifications.png'):
        """
        Analyze and visualize misclassified examples
        
        Args:
            X_test (np.array): Test images
            y_true (np.array): True labels
            y_pred (np.array): Predicted labels
            y_pred_proba (np.array): Prediction probabilities
            num_examples (int): Number of examples to show
            save_path (str): Path to save the plot
        """
        # Find misclassified examples
        misclassified_indices = np.where(y_true != y_pred)[0]
        
        if len(misclassified_indices) == 0:
            print("No misclassifications found!")
            return
        
        # Sort by confidence (show most confident wrong predictions)
        confidences = np.max(y_pred_proba[misclassified_indices], axis=1)
        sorted_indices = misclassified_indices[np.argsort(confidences)[::-1]]
        
        # Select examples to display
        examples_to_show = min(num_examples, len(sorted_indices))
        selected_indices = sorted_indices[:examples_to_show]
        
        # Plot misclassified examples
        fig, axes = plt.subplots(2, 5, figsize=(15, 8))
        axes = axes.flatten()
        
        for i, idx in enumerate(selected_indices):
            if i >= 10:  # Limit to 10 examples
                break
                
            ax = axes[i]
            
            # Display image
            ax.imshow(X_test[idx])
            
            # Add title with true and predicted labels
            true_label = self.class_names[y_true[idx]]
            pred_label = self.class_names[y_pred[idx]]
            confidence = np.max(y_pred_proba[idx])
            
            ax.set_title(f'True: {true_label}\nPred: {pred_label}\nConf: {confidence:.3f}', 
                        fontsize=10)
            ax.axis('off')
        
        # Hide unused subplots
        for i in range(examples_to_show, 10):
            axes[i].axis('off')
        
        plt.suptitle('Most Confident Misclassifications', fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Misclassifications plot saved to {save_path}")
        
        plt.show()
        
        # Print misclassification statistics
        print(f"\nMisclassification Analysis:")
        print(f"Total misclassifications: {len(misclassified_indices)}")
        print(f"Misclassification rate: {len(misclassified_indices)/len(y_true)*100:.2f}%")
    
    def save_evaluation_results(self, results):
        """
        Save evaluation results to files
        
        Args:
            results (dict): Evaluation results dictionary
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save classification report
        with open(f'logs/classification_report_{timestamp}.json', 'w') as f:
            json.dump(results['classification_report'], f, indent=2)
        
        # Save confusion matrix
        np.save(f'logs/confusion_matrix_{timestamp}.npy', results['confusion_matrix'])
        
        # Save per-class accuracy
        per_class_df = pd.DataFrame({
            'Class': self.class_names,
            'Accuracy': results['per_class_accuracy']
        })
        per_class_df.to_csv(f'logs/per_class_accuracy_{timestamp}.csv', index=False)
        
        # Save summary metrics
        summary = {
            'timestamp': timestamp,
            'test_accuracy': float(results['test_accuracy']),
            'test_loss': float(results['test_loss']),
            'total_samples': len(results['y_true']),
            'num_classes': len(self.class_names),
            'model_path': self.model_path
        }
        
        with open(f'logs/evaluation_summary_{timestamp}.json', 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"Evaluation results saved with timestamp: {timestamp}")

def create_comprehensive_evaluation_report(model_path, X_test, y_test, class_names):
    """
    Create a comprehensive evaluation report
    
    Args:
        model_path (str): Path to the trained model
        X_test (np.array): Test images
        y_test (np.array): Test labels
        class_names (list): List of class names
    """
    print("Creating comprehensive evaluation report...")
    
    # Initialize evaluator
    evaluator = ModelEvaluator(model_path, class_names)
    
    # Evaluate model
    results = evaluator.evaluate_on_test_set(X_test, y_test)
    
    # Generate all plots
    evaluator.plot_confusion_matrix(results['confusion_matrix'])
    evaluator.plot_per_class_metrics(results['classification_report'])
    evaluator.plot_prediction_confidence_distribution(results['y_pred_proba'])
    evaluator.analyze_misclassifications(
        X_test, results['y_true'], results['y_pred'], results['y_pred_proba']
    )
    
    print("Comprehensive evaluation report completed!")
    
    return results
