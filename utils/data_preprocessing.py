"""
Data preprocessing utilities for ASL Sign Language Detection
"""

import os
import numpy as np
import pandas as pd
from PIL import Image
import cv2
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import tensorflow as tf
from tensorflow.keras.utils import to_categorical
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm

class ASLDataProcessor:
    def __init__(self, dataset_path, img_size=(128, 128)):
        """
        Initialize ASL Data Processor
        
        Args:
            dataset_path (str): Path to the ASL dataset
            img_size (tuple): Target image size (height, width)
        """
        self.dataset_path = dataset_path
        self.img_size = img_size
        self.label_encoder = LabelEncoder()
        self.class_names = []
        
    def load_dataset(self):
        """
        Load the complete ASL dataset from directory structure
        
        Returns:
            tuple: (images, labels, class_names)
        """
        images = []
        labels = []
        
        # Get all class directories (a-z, 0-9)
        class_dirs = [d for d in os.listdir(self.dataset_path) 
                     if os.path.isdir(os.path.join(self.dataset_path, d)) and d != 'asl_dataset']
        
        # Sort to ensure consistent ordering
        class_dirs.sort()
        self.class_names = class_dirs
        
        print(f"Found {len(class_dirs)} classes: {class_dirs}")
        
        for class_name in tqdm(class_dirs, desc="Loading classes"):
            class_path = os.path.join(self.dataset_path, class_name)
            
            # Get all image files in the class directory
            image_files = [f for f in os.listdir(class_path) 
                          if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            
            for img_file in image_files:
                img_path = os.path.join(class_path, img_file)
                
                try:
                    # Load and preprocess image
                    img = self.load_and_preprocess_image(img_path)
                    images.append(img)
                    labels.append(class_name)
                except Exception as e:
                    print(f"Error loading {img_path}: {e}")
                    continue
        
        # Convert to numpy arrays
        images = np.array(images)
        labels = np.array(labels)
        
        print(f"Loaded {len(images)} images with {len(set(labels))} unique classes")
        
        return images, labels, self.class_names
    
    def load_and_preprocess_image(self, img_path):
        """
        Load and preprocess a single image
        
        Args:
            img_path (str): Path to the image file
            
        Returns:
            np.array: Preprocessed image
        """
        # Load image using PIL
        img = Image.open(img_path)
        
        # Convert to RGB if needed
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        # Resize image
        img = img.resize(self.img_size)
        
        # Convert to numpy array and normalize
        img_array = np.array(img, dtype=np.float32) / 255.0
        
        return img_array
    
    def prepare_labels(self, labels):
        """
        Encode labels and convert to categorical
        
        Args:
            labels (np.array): Raw string labels
            
        Returns:
            tuple: (encoded_labels, categorical_labels, num_classes)
        """
        # Fit label encoder
        encoded_labels = self.label_encoder.fit_transform(labels)
        
        # Convert to categorical (one-hot encoding)
        num_classes = len(self.class_names)
        categorical_labels = to_categorical(encoded_labels, num_classes)
        
        return encoded_labels, categorical_labels, num_classes
    
    def split_data(self, images, labels, test_size=0.15, val_size=0.15, random_state=42):
        """
        Split data into train, validation, and test sets
        
        Args:
            images (np.array): Image data
            labels (np.array): Label data
            test_size (float): Proportion for test set
            val_size (float): Proportion for validation set
            random_state (int): Random seed
            
        Returns:
            tuple: (X_train, X_val, X_test, y_train, y_val, y_test)
        """
        # First split: separate test set
        X_temp, X_test, y_temp, y_test = train_test_split(
            images, labels, test_size=test_size, random_state=random_state, stratify=labels
        )
        
        # Second split: separate train and validation from remaining data
        val_size_adjusted = val_size / (1 - test_size)  # Adjust val_size for remaining data
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_size_adjusted, random_state=random_state, stratify=y_temp
        )
        
        print(f"Data split - Train: {len(X_train)}, Val: {len(X_val)}, Test: {len(X_test)}")
        
        return X_train, X_val, X_test, y_train, y_val, y_test
    
    def create_data_generators(self, X_train, y_train, X_val, y_val, batch_size=32):
        """
        Create data generators with augmentation for training
        
        Args:
            X_train, y_train: Training data
            X_val, y_val: Validation data
            batch_size (int): Batch size for generators
            
        Returns:
            tuple: (train_generator, val_generator)
        """
        # Data augmentation for training
        train_datagen = ImageDataGenerator(
            rotation_range=20,
            width_shift_range=0.1,
            height_shift_range=0.1,
            shear_range=0.1,
            zoom_range=0.1,
            horizontal_flip=False,  # Don't flip for sign language
            brightness_range=[0.8, 1.2],
            fill_mode='nearest'
        )
        
        # No augmentation for validation
        val_datagen = ImageDataGenerator()
        
        # Create generators
        train_generator = train_datagen.flow(X_train, y_train, batch_size=batch_size)
        val_generator = val_datagen.flow(X_val, y_val, batch_size=batch_size)
        
        return train_generator, val_generator
    
    def visualize_dataset(self, images, labels, num_samples=16):
        """
        Visualize sample images from the dataset
        
        Args:
            images (np.array): Image data
            labels (np.array): Label data
            num_samples (int): Number of samples to display
        """
        plt.figure(figsize=(12, 8))
        
        # Randomly select samples
        indices = np.random.choice(len(images), num_samples, replace=False)
        
        for i, idx in enumerate(indices):
            plt.subplot(4, 4, i + 1)
            plt.imshow(images[idx])
            plt.title(f'Label: {labels[idx]}')
            plt.axis('off')
        
        plt.tight_layout()
        plt.savefig('logs/dataset_samples.png', dpi=150, bbox_inches='tight')
        plt.show()
    
    def analyze_class_distribution(self, labels):
        """
        Analyze and visualize class distribution
        
        Args:
            labels (np.array): Label data
        """
        # Count samples per class
        unique, counts = np.unique(labels, return_counts=True)
        
        # Create DataFrame for easier handling
        df = pd.DataFrame({'Class': unique, 'Count': counts})
        df = df.sort_values('Class')
        
        # Plot distribution
        plt.figure(figsize=(15, 6))
        sns.barplot(data=df, x='Class', y='Count')
        plt.title('Class Distribution in ASL Dataset')
        plt.xlabel('ASL Sign')
        plt.ylabel('Number of Images')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('logs/class_distribution.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print(f"Total classes: {len(unique)}")
        print(f"Total images: {len(labels)}")
        print(f"Average images per class: {len(labels) / len(unique):.1f}")
        print(f"Min images per class: {counts.min()}")
        print(f"Max images per class: {counts.max()}")
        
        return df

def preprocess_frame_for_prediction(frame, img_size=(128, 128)):
    """
    Preprocess a single frame for real-time prediction
    
    Args:
        frame (np.array): Input frame from webcam
        img_size (tuple): Target image size
        
    Returns:
        np.array: Preprocessed frame ready for model prediction
    """
    # Convert BGR to RGB
    if len(frame.shape) == 3 and frame.shape[2] == 3:
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    else:
        frame_rgb = frame
    
    # Resize frame
    frame_resized = cv2.resize(frame_rgb, img_size)
    
    # Normalize pixel values
    frame_normalized = frame_resized.astype(np.float32) / 255.0
    
    # Add batch dimension
    frame_batch = np.expand_dims(frame_normalized, axis=0)
    
    return frame_batch
