"""
Real-time Processing Utilities for ASL Sign Language Detection
"""

import cv2
import numpy as np
import tensorflow as tf
from collections import deque
import time
import mediapipe as mp

class RealTimeProcessor:
    def __init__(self, model_path, class_names, img_size=(128, 128), 
                 confidence_threshold=0.5, smoothing_window=5):
        """
        Initialize Real-time Processor
        
        Args:
            model_path (str): Path to the trained model
            class_names (list): List of class names
            img_size (tuple): Target image size for model input
            confidence_threshold (float): Minimum confidence for predictions
            smoothing_window (int): Number of frames for prediction smoothing
        """
        self.model_path = model_path
        self.class_names = class_names
        self.img_size = img_size
        self.confidence_threshold = confidence_threshold
        self.smoothing_window = smoothing_window
        
        # Load model
        self.model = None
        self.load_model()
        
        # Initialize MediaPipe hands
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=1,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Prediction smoothing
        self.prediction_history = deque(maxlen=smoothing_window)
        
        # Performance tracking
        self.fps_counter = deque(maxlen=30)
        self.processing_times = deque(maxlen=30)
        
    def load_model(self):
        """Load the trained model"""
        try:
            self.model = tf.keras.models.load_model(self.model_path)
            print(f"Model loaded successfully from {self.model_path}")
        except Exception as e:
            print(f"Error loading model: {e}")
            self.model = None
    
    def detect_hand_region(self, frame):
        """
        Detect hand region using MediaPipe
        
        Args:
            frame (np.array): Input frame
            
        Returns:
            tuple: (hand_region, hand_landmarks, bbox)
        """
        # Convert BGR to RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Process frame
        results = self.hands.process(rgb_frame)
        
        if results.multi_hand_landmarks:
            # Get first hand
            hand_landmarks = results.multi_hand_landmarks[0]
            
            # Get bounding box
            h, w, _ = frame.shape
            x_coords = [landmark.x * w for landmark in hand_landmarks.landmark]
            y_coords = [landmark.y * h for landmark in hand_landmarks.landmark]
            
            # Add padding
            padding = 50
            x_min = max(0, int(min(x_coords)) - padding)
            x_max = min(w, int(max(x_coords)) + padding)
            y_min = max(0, int(min(y_coords)) - padding)
            y_max = min(h, int(max(y_coords)) + padding)
            
            # Extract hand region
            hand_region = frame[y_min:y_max, x_min:x_max]
            bbox = (x_min, y_min, x_max, y_max)
            
            return hand_region, hand_landmarks, bbox
        
        return None, None, None
    
    def preprocess_frame(self, frame):
        """
        Preprocess frame for model prediction
        
        Args:
            frame (np.array): Input frame
            
        Returns:
            np.array: Preprocessed frame ready for prediction
        """
        if frame is None or frame.size == 0:
            return None
        
        try:
            # Convert BGR to RGB
            if len(frame.shape) == 3 and frame.shape[2] == 3:
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            else:
                frame_rgb = frame
            
            # Resize to model input size
            frame_resized = cv2.resize(frame_rgb, self.img_size)
            
            # Normalize pixel values
            frame_normalized = frame_resized.astype(np.float32) / 255.0
            
            # Add batch dimension
            frame_batch = np.expand_dims(frame_normalized, axis=0)
            
            return frame_batch
            
        except Exception as e:
            print(f"Error in preprocessing: {e}")
            return None
    
    def predict_sign(self, preprocessed_frame):
        """
        Predict sign from preprocessed frame
        
        Args:
            preprocessed_frame (np.array): Preprocessed frame
            
        Returns:
            tuple: (predicted_class, confidence, all_probabilities)
        """
        if self.model is None or preprocessed_frame is None:
            return None, 0.0, None
        
        try:
            start_time = time.time()
            
            # Make prediction
            predictions = self.model.predict(preprocessed_frame, verbose=0)
            
            # Get prediction details
            predicted_class_idx = np.argmax(predictions[0])
            confidence = predictions[0][predicted_class_idx]
            predicted_class = self.class_names[predicted_class_idx]
            
            # Track processing time
            processing_time = time.time() - start_time
            self.processing_times.append(processing_time)
            
            return predicted_class, confidence, predictions[0]
            
        except Exception as e:
            print(f"Error in prediction: {e}")
            return None, 0.0, None
    
    def smooth_predictions(self, predicted_class, confidence):
        """
        Smooth predictions over multiple frames
        
        Args:
            predicted_class (str): Current predicted class
            confidence (float): Current prediction confidence
            
        Returns:
            tuple: (smoothed_class, smoothed_confidence)
        """
        # Add current prediction to history
        self.prediction_history.append((predicted_class, confidence))
        
        if len(self.prediction_history) < 2:
            return predicted_class, confidence
        
        # Count occurrences of each class
        class_counts = {}
        total_confidence = {}
        
        for cls, conf in self.prediction_history:
            if cls not in class_counts:
                class_counts[cls] = 0
                total_confidence[cls] = 0.0
            class_counts[cls] += 1
            total_confidence[cls] += conf
        
        # Find most frequent class
        most_frequent_class = max(class_counts, key=class_counts.get)
        avg_confidence = total_confidence[most_frequent_class] / class_counts[most_frequent_class]
        
        return most_frequent_class, avg_confidence
    
    def process_frame(self, frame):
        """
        Complete frame processing pipeline
        
        Args:
            frame (np.array): Input frame from webcam
            
        Returns:
            dict: Processing results
        """
        start_time = time.time()
        
        results = {
            'original_frame': frame,
            'hand_region': None,
            'hand_landmarks': None,
            'bbox': None,
            'predicted_class': None,
            'confidence': 0.0,
            'smoothed_class': None,
            'smoothed_confidence': 0.0,
            'all_probabilities': None,
            'processing_time': 0.0,
            'fps': 0.0
        }
        
        # Detect hand region
        hand_region, hand_landmarks, bbox = self.detect_hand_region(frame)
        results['hand_landmarks'] = hand_landmarks
        results['bbox'] = bbox
        
        if hand_region is not None:
            results['hand_region'] = hand_region
            
            # Preprocess hand region
            preprocessed_frame = self.preprocess_frame(hand_region)
            
            if preprocessed_frame is not None:
                # Make prediction
                predicted_class, confidence, all_probs = self.predict_sign(preprocessed_frame)
                
                if predicted_class is not None:
                    results['predicted_class'] = predicted_class
                    results['confidence'] = confidence
                    results['all_probabilities'] = all_probs
                    
                    # Apply smoothing
                    smoothed_class, smoothed_confidence = self.smooth_predictions(
                        predicted_class, confidence
                    )
                    results['smoothed_class'] = smoothed_class
                    results['smoothed_confidence'] = smoothed_confidence
        
        # Calculate performance metrics
        processing_time = time.time() - start_time
        results['processing_time'] = processing_time
        
        # Calculate FPS
        self.fps_counter.append(time.time())
        if len(self.fps_counter) > 1:
            fps = len(self.fps_counter) / (self.fps_counter[-1] - self.fps_counter[0])
            results['fps'] = fps
        
        return results
    
    def draw_results(self, frame, results):
        """
        Draw prediction results on frame
        
        Args:
            frame (np.array): Input frame
            results (dict): Processing results
            
        Returns:
            np.array: Frame with drawn results
        """
        output_frame = frame.copy()
        
        # Draw hand landmarks
        if results['hand_landmarks'] is not None:
            self.mp_drawing.draw_landmarks(
                output_frame, results['hand_landmarks'], self.mp_hands.HAND_CONNECTIONS
            )
        
        # Draw bounding box
        if results['bbox'] is not None:
            x_min, y_min, x_max, y_max = results['bbox']
            cv2.rectangle(output_frame, (x_min, y_min), (x_max, y_max), (0, 255, 0), 2)
        
        # Draw prediction text
        if results['smoothed_class'] is not None and results['smoothed_confidence'] > self.confidence_threshold:
            text = f"{results['smoothed_class']}: {results['smoothed_confidence']:.2f}"
            cv2.putText(output_frame, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 
                       1, (0, 255, 0), 2)
        
        # Draw performance metrics
        fps_text = f"FPS: {results['fps']:.1f}"
        cv2.putText(output_frame, fps_text, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 
                   0.7, (255, 255, 255), 2)
        
        processing_time_text = f"Processing: {results['processing_time']*1000:.1f}ms"
        cv2.putText(output_frame, processing_time_text, (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 
                   0.7, (255, 255, 255), 2)
        
        return output_frame
    
    def get_performance_stats(self):
        """
        Get current performance statistics
        
        Returns:
            dict: Performance statistics
        """
        stats = {
            'avg_fps': 0.0,
            'avg_processing_time': 0.0,
            'min_processing_time': 0.0,
            'max_processing_time': 0.0
        }
        
        if len(self.fps_counter) > 1:
            stats['avg_fps'] = len(self.fps_counter) / (self.fps_counter[-1] - self.fps_counter[0])
        
        if len(self.processing_times) > 0:
            stats['avg_processing_time'] = np.mean(self.processing_times) * 1000  # ms
            stats['min_processing_time'] = np.min(self.processing_times) * 1000  # ms
            stats['max_processing_time'] = np.max(self.processing_times) * 1000  # ms
        
        return stats
    
    def reset_history(self):
        """Reset prediction history and performance counters"""
        self.prediction_history.clear()
        self.fps_counter.clear()
        self.processing_times.clear()

class FrameBuffer:
    """Buffer for storing recent frames for analysis"""
    
    def __init__(self, max_size=10):
        self.max_size = max_size
        self.frames = deque(maxlen=max_size)
        self.timestamps = deque(maxlen=max_size)
    
    def add_frame(self, frame):
        """Add frame to buffer"""
        self.frames.append(frame.copy())
        self.timestamps.append(time.time())
    
    def get_recent_frames(self, num_frames=5):
        """Get most recent frames"""
        if len(self.frames) < num_frames:
            return list(self.frames)
        return list(self.frames)[-num_frames:]
    
    def clear(self):
        """Clear buffer"""
        self.frames.clear()
        self.timestamps.clear()
