"""
CNN Model Architecture for ASL Sign Language Detection
"""

import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import (
    Conv2D, MaxPooling2D, Dropout, Flatten, Dense, 
    BatchNormalization, GlobalAveragePooling2D, Input
)
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import (
    ModelCheckpoint, EarlyStopping, ReduceLROnPlateau, 
    CSVLogger, TensorBoard
)
import os

class ASLCNNModel:
    def __init__(self, input_shape=(128, 128, 3), num_classes=36):
        """
        Initialize ASL CNN Model
        
        Args:
            input_shape (tuple): Input image shape (height, width, channels)
            num_classes (int): Number of classes (A-Z: 26, 0-9: 10, total: 36)
        """
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.model = None
        
    def create_basic_cnn(self):
        """
        Create a basic CNN architecture for ASL classification
        
        Returns:
            tf.keras.Model: Compiled CNN model
        """
        model = Sequential([
            # First Convolutional Block
            Conv2D(32, (3, 3), activation='relu', input_shape=self.input_shape),
            BatchNormalization(),
            Conv2D(32, (3, 3), activation='relu'),
            MaxPooling2D(2, 2),
            Dropout(0.25),
            
            # Second Convolutional Block
            Conv2D(64, (3, 3), activation='relu'),
            BatchNormalization(),
            Conv2D(64, (3, 3), activation='relu'),
            MaxPooling2D(2, 2),
            Dropout(0.25),
            
            # Third Convolutional Block
            Conv2D(128, (3, 3), activation='relu'),
            BatchNormalization(),
            Conv2D(128, (3, 3), activation='relu'),
            MaxPooling2D(2, 2),
            Dropout(0.25),
            
            # Fourth Convolutional Block
            Conv2D(256, (3, 3), activation='relu'),
            BatchNormalization(),
            Conv2D(256, (3, 3), activation='relu'),
            MaxPooling2D(2, 2),
            Dropout(0.25),
            
            # Flatten and Dense Layers
            Flatten(),
            Dense(512, activation='relu'),
            BatchNormalization(),
            Dropout(0.5),
            Dense(256, activation='relu'),
            BatchNormalization(),
            Dropout(0.5),
            Dense(self.num_classes, activation='softmax')
        ])
        
        self.model = model
        return model
    
    def create_advanced_cnn(self):
        """
        Create an advanced CNN architecture with residual-like connections
        
        Returns:
            tf.keras.Model: Compiled advanced CNN model
        """
        inputs = Input(shape=self.input_shape)
        
        # First Block
        x = Conv2D(32, (3, 3), activation='relu', padding='same')(inputs)
        x = BatchNormalization()(x)
        x = Conv2D(32, (3, 3), activation='relu', padding='same')(x)
        x = BatchNormalization()(x)
        x = MaxPooling2D(2, 2)(x)
        x = Dropout(0.25)(x)
        
        # Second Block
        x = Conv2D(64, (3, 3), activation='relu', padding='same')(x)
        x = BatchNormalization()(x)
        x = Conv2D(64, (3, 3), activation='relu', padding='same')(x)
        x = BatchNormalization()(x)
        x = MaxPooling2D(2, 2)(x)
        x = Dropout(0.25)(x)
        
        # Third Block
        x = Conv2D(128, (3, 3), activation='relu', padding='same')(x)
        x = BatchNormalization()(x)
        x = Conv2D(128, (3, 3), activation='relu', padding='same')(x)
        x = BatchNormalization()(x)
        x = MaxPooling2D(2, 2)(x)
        x = Dropout(0.25)(x)
        
        # Fourth Block
        x = Conv2D(256, (3, 3), activation='relu', padding='same')(x)
        x = BatchNormalization()(x)
        x = Conv2D(256, (3, 3), activation='relu', padding='same')(x)
        x = BatchNormalization()(x)
        x = MaxPooling2D(2, 2)(x)
        x = Dropout(0.25)(x)
        
        # Fifth Block
        x = Conv2D(512, (3, 3), activation='relu', padding='same')(x)
        x = BatchNormalization()(x)
        x = GlobalAveragePooling2D()(x)
        x = Dropout(0.5)(x)
        
        # Dense Layers
        x = Dense(512, activation='relu')(x)
        x = BatchNormalization()(x)
        x = Dropout(0.5)(x)
        x = Dense(256, activation='relu')(x)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)
        
        # Output Layer
        outputs = Dense(self.num_classes, activation='softmax')(x)
        
        model = Model(inputs=inputs, outputs=outputs)
        self.model = model
        return model
    
    def compile_model(self, learning_rate=0.001):
        """
        Compile the model with optimizer, loss, and metrics
        
        Args:
            learning_rate (float): Learning rate for Adam optimizer
        """
        if self.model is None:
            raise ValueError("Model not created. Call create_basic_cnn() or create_advanced_cnn() first.")
        
        optimizer = Adam(learning_rate=learning_rate)
        
        self.model.compile(
            optimizer=optimizer,
            loss='categorical_crossentropy',
            metrics=['accuracy', 'top_3_accuracy']
        )
        
        print("Model compiled successfully!")
        return self.model
    
    def get_model_summary(self):
        """
        Print model summary and return total parameters
        
        Returns:
            int: Total number of parameters
        """
        if self.model is None:
            raise ValueError("Model not created.")
        
        self.model.summary()
        
        total_params = self.model.count_params()
        print(f"\nTotal parameters: {total_params:,}")
        
        return total_params
    
    def create_callbacks(self, model_save_path='models/best_asl_model.keras'):
        """
        Create training callbacks for model optimization
        
        Args:
            model_save_path (str): Path to save the best model
            
        Returns:
            list: List of Keras callbacks
        """
        # Ensure models directory exists
        os.makedirs(os.path.dirname(model_save_path), exist_ok=True)
        
        callbacks = [
            # Save best model
            ModelCheckpoint(
                filepath=model_save_path,
                monitor='val_accuracy',
                save_best_only=True,
                save_weights_only=False,
                mode='max',
                verbose=1
            ),
            
            # Early stopping
            EarlyStopping(
                monitor='val_accuracy',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            
            # Reduce learning rate on plateau
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=8,
                min_lr=1e-7,
                verbose=1
            ),
            
            # CSV Logger
            CSVLogger(
                filename='logs/training_history.csv',
                separator=',',
                append=False
            ),
            
            # TensorBoard
            TensorBoard(
                log_dir='logs/tensorboard',
                histogram_freq=1,
                write_graph=True,
                write_images=True
            )
        ]
        
        return callbacks
    
    def load_model(self, model_path):
        """
        Load a pre-trained model
        
        Args:
            model_path (str): Path to the saved model
            
        Returns:
            tf.keras.Model: Loaded model
        """
        self.model = tf.keras.models.load_model(model_path)
        print(f"Model loaded from {model_path}")
        return self.model
    
    def save_model(self, save_path='models/asl_model_final.keras'):
        """
        Save the current model
        
        Args:
            save_path (str): Path to save the model
        """
        if self.model is None:
            raise ValueError("No model to save.")
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        self.model.save(save_path)
        print(f"Model saved to {save_path}")

def create_transfer_learning_model(input_shape=(128, 128, 3), num_classes=36, base_model_name='MobileNetV2'):
    """
    Create a transfer learning model using pre-trained weights
    
    Args:
        input_shape (tuple): Input image shape
        num_classes (int): Number of output classes
        base_model_name (str): Name of the base model ('MobileNetV2', 'ResNet50', 'EfficientNetB0')
        
    Returns:
        tf.keras.Model: Transfer learning model
    """
    # Load pre-trained base model
    if base_model_name == 'MobileNetV2':
        base_model = tf.keras.applications.MobileNetV2(
            input_shape=input_shape,
            include_top=False,
            weights='imagenet'
        )
    elif base_model_name == 'ResNet50':
        base_model = tf.keras.applications.ResNet50(
            input_shape=input_shape,
            include_top=False,
            weights='imagenet'
        )
    elif base_model_name == 'EfficientNetB0':
        base_model = tf.keras.applications.EfficientNetB0(
            input_shape=input_shape,
            include_top=False,
            weights='imagenet'
        )
    else:
        raise ValueError(f"Unsupported base model: {base_model_name}")
    
    # Freeze base model layers
    base_model.trainable = False
    
    # Add custom classification head
    inputs = Input(shape=input_shape)
    x = base_model(inputs, training=False)
    x = GlobalAveragePooling2D()(x)
    x = Dropout(0.5)(x)
    x = Dense(512, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.3)(x)
    outputs = Dense(num_classes, activation='softmax')(x)
    
    model = Model(inputs, outputs)
    
    return model
