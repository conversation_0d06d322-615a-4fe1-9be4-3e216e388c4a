"""
Complete Training Script for ASL Sign Language Detection Model
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json

# Add utils to path
sys.path.append('utils')

from data_preprocessing import ASLDataProcessor
from model_architecture import ASLCNNModel, create_transfer_learning_model

import tensorflow as tf
from sklearn.metrics import classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

class ASLTrainer:
    def __init__(self, dataset_path, img_size=(128, 128), model_type='basic'):
        """
        Initialize ASL Trainer
        
        Args:
            dataset_path (str): Path to ASL dataset
            img_size (tuple): Image size for training
            model_type (str): Type of model ('basic', 'advanced', 'transfer')
        """
        self.dataset_path = dataset_path
        self.img_size = img_size
        self.model_type = model_type
        
        # Initialize data processor
        self.data_processor = ASLDataProcessor(dataset_path, img_size)
        
        # Initialize model
        self.model_builder = ASLCNNModel(input_shape=(*img_size, 3))
        self.model = None
        
        # Training history
        self.history = None
        self.class_names = []
        
        # Create necessary directories
        os.makedirs('models', exist_ok=True)
        os.makedirs('logs', exist_ok=True)
        
    def load_and_prepare_data(self):
        """
        Load and prepare the dataset for training
        """
        print("Loading ASL dataset...")
        
        # Load dataset
        images, labels, self.class_names = self.data_processor.load_dataset()
        
        # Analyze dataset
        print("\nAnalyzing dataset...")
        self.data_processor.analyze_class_distribution(labels)
        
        # Visualize samples
        self.data_processor.visualize_dataset(images, labels)
        
        # Prepare labels
        encoded_labels, categorical_labels, num_classes = self.data_processor.prepare_labels(labels)
        
        # Update model with correct number of classes
        self.model_builder.num_classes = num_classes
        
        # Split data
        print("\nSplitting data...")
        X_train, X_val, X_test, y_train, y_val, y_test = self.data_processor.split_data(
            images, categorical_labels, test_size=0.15, val_size=0.15
        )
        
        # Store data for later use
        self.X_train, self.X_val, self.X_test = X_train, X_val, X_test
        self.y_train, self.y_val, self.y_test = y_train, y_val, y_test
        
        print(f"Training set: {X_train.shape}")
        print(f"Validation set: {X_val.shape}")
        print(f"Test set: {X_test.shape}")
        print(f"Number of classes: {num_classes}")
        
        return X_train, X_val, X_test, y_train, y_val, y_test
    
    def create_model(self):
        """
        Create the model based on specified type
        """
        print(f"\nCreating {self.model_type} model...")
        
        if self.model_type == 'basic':
            self.model = self.model_builder.create_basic_cnn()
        elif self.model_type == 'advanced':
            self.model = self.model_builder.create_advanced_cnn()
        elif self.model_type == 'transfer':
            self.model = create_transfer_learning_model(
                input_shape=(*self.img_size, 3),
                num_classes=self.model_builder.num_classes
            )
            self.model_builder.model = self.model
        else:
            raise ValueError(f"Unknown model type: {self.model_type}")
        
        # Compile model
        self.model_builder.compile_model()
        
        # Print model summary
        self.model_builder.get_model_summary()
        
        return self.model
    
    def train_model(self, epochs=50, batch_size=32):
        """
        Train the model
        
        Args:
            epochs (int): Number of training epochs
            batch_size (int): Batch size for training
        """
        print(f"\nStarting training for {epochs} epochs...")
        
        # Create data generators
        train_generator, val_generator = self.data_processor.create_data_generators(
            self.X_train, self.y_train, self.X_val, self.y_val, batch_size
        )
        
        # Create callbacks
        callbacks = self.model_builder.create_callbacks()
        
        # Calculate steps per epoch
        steps_per_epoch = len(self.X_train) // batch_size
        validation_steps = len(self.X_val) // batch_size
        
        # Train model
        start_time = datetime.now()
        
        self.history = self.model.fit(
            train_generator,
            steps_per_epoch=steps_per_epoch,
            epochs=epochs,
            validation_data=val_generator,
            validation_steps=validation_steps,
            callbacks=callbacks,
            verbose=1
        )
        
        end_time = datetime.now()
        training_time = end_time - start_time
        
        print(f"\nTraining completed in {training_time}")
        
        # Save training history
        self.save_training_history()
        
        return self.history
    
    def evaluate_model(self):
        """
        Evaluate the trained model on test set
        """
        print("\nEvaluating model on test set...")
        
        # Load best model
        best_model_path = 'models/best_asl_model.keras'
        if os.path.exists(best_model_path):
            self.model = tf.keras.models.load_model(best_model_path)
            print("Loaded best model for evaluation")
        
        # Evaluate on test set
        test_loss, test_accuracy, test_top3_accuracy = self.model.evaluate(
            self.X_test, self.y_test, verbose=0
        )
        
        print(f"Test Loss: {test_loss:.4f}")
        print(f"Test Accuracy: {test_accuracy:.4f}")
        print(f"Test Top-3 Accuracy: {test_top3_accuracy:.4f}")
        
        # Generate predictions
        y_pred = self.model.predict(self.X_test)
        y_pred_classes = np.argmax(y_pred, axis=1)
        y_true_classes = np.argmax(self.y_test, axis=1)
        
        # Classification report
        print("\nClassification Report:")
        report = classification_report(
            y_true_classes, y_pred_classes, 
            target_names=self.class_names, 
            output_dict=True
        )
        print(classification_report(y_true_classes, y_pred_classes, target_names=self.class_names))
        
        # Save classification report
        with open('logs/classification_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        # Confusion matrix
        self.plot_confusion_matrix(y_true_classes, y_pred_classes)
        
        return test_accuracy, report
    
    def plot_confusion_matrix(self, y_true, y_pred):
        """
        Plot and save confusion matrix
        """
        cm = confusion_matrix(y_true, y_pred)
        
        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=self.class_names, yticklabels=self.class_names)
        plt.title('Confusion Matrix')
        plt.xlabel('Predicted')
        plt.ylabel('Actual')
        plt.tight_layout()
        plt.savefig('logs/confusion_matrix.png', dpi=150, bbox_inches='tight')
        plt.show()
    
    def plot_training_history(self):
        """
        Plot training history
        """
        if self.history is None:
            print("No training history available")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Accuracy
        axes[0, 0].plot(self.history.history['accuracy'], label='Training Accuracy')
        axes[0, 0].plot(self.history.history['val_accuracy'], label='Validation Accuracy')
        axes[0, 0].set_title('Model Accuracy')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # Loss
        axes[0, 1].plot(self.history.history['loss'], label='Training Loss')
        axes[0, 1].plot(self.history.history['val_loss'], label='Validation Loss')
        axes[0, 1].set_title('Model Loss')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # Top-3 Accuracy
        axes[1, 0].plot(self.history.history['top_3_accuracy'], label='Training Top-3 Accuracy')
        axes[1, 0].plot(self.history.history['val_top_3_accuracy'], label='Validation Top-3 Accuracy')
        axes[1, 0].set_title('Model Top-3 Accuracy')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Top-3 Accuracy')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        
        # Learning Rate (if available)
        if 'lr' in self.history.history:
            axes[1, 1].plot(self.history.history['lr'], label='Learning Rate')
            axes[1, 1].set_title('Learning Rate')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Learning Rate')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
        else:
            axes[1, 1].axis('off')
        
        plt.tight_layout()
        plt.savefig('logs/training_history.png', dpi=150, bbox_inches='tight')
        plt.show()
    
    def save_training_history(self):
        """
        Save training history to file
        """
        if self.history is None:
            return
        
        # Convert history to DataFrame
        history_df = pd.DataFrame(self.history.history)
        history_df.to_csv('logs/training_history_detailed.csv', index=False)
        
        print("Training history saved to logs/training_history_detailed.csv")

def main():
    """
    Main training function
    """
    print("ASL Sign Language Detection - Model Training")
    print("=" * 50)
    
    # Configuration
    DATASET_PATH = 'asl_dataset'
    IMG_SIZE = (128, 128)
    MODEL_TYPE = 'advanced'  # 'basic', 'advanced', or 'transfer'
    EPOCHS = 50
    BATCH_SIZE = 32
    
    # Initialize trainer
    trainer = ASLTrainer(DATASET_PATH, IMG_SIZE, MODEL_TYPE)
    
    try:
        # Load and prepare data
        trainer.load_and_prepare_data()
        
        # Create model
        trainer.create_model()
        
        # Train model
        trainer.train_model(epochs=EPOCHS, batch_size=BATCH_SIZE)
        
        # Plot training history
        trainer.plot_training_history()
        
        # Evaluate model
        trainer.evaluate_model()
        
        # Save final model
        trainer.model_builder.save_model('models/asl_model_final.keras')
        
        print("\nTraining completed successfully!")
        print("Best model saved to: models/best_asl_model.keras")
        print("Final model saved to: models/asl_model_final.keras")
        
    except Exception as e:
        print(f"Error during training: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
