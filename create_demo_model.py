"""
Create a demo model for ASL detection to demonstrate the web application
"""

import os
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, Dropout
from tensorflow.keras.optimizers import Adam

# Set random seed
np.random.seed(42)
tf.random.set_seed(42)

def create_demo_model():
    """Create a simple demo model for ASL detection"""
    
    # ASL classes (A-Z, 0-9)
    class_names = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'] + \
                  [chr(i) for i in range(ord('a'), ord('z') + 1)]
    
    num_classes = len(class_names)
    input_shape = (128, 128, 3)
    
    print(f"Creating demo model for {num_classes} classes")
    print(f"Classes: {class_names}")
    
    # Create a simple CNN model
    model = Sequential([
        Conv2D(32, (3, 3), activation='relu', input_shape=input_shape),
        MaxPooling2D(2, 2),
        Dropout(0.25),
        
        Conv2D(64, (3, 3), activation='relu'),
        MaxPooling2D(2, 2),
        Dropout(0.25),
        
        Conv2D(128, (3, 3), activation='relu'),
        MaxPooling2D(2, 2),
        Dropout(0.25),
        
        Flatten(),
        Dense(512, activation='relu'),
        Dropout(0.5),
        Dense(256, activation='relu'),
        Dropout(0.5),
        Dense(num_classes, activation='softmax')
    ])
    
    # Compile the model
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # Print model summary
    model.summary()
    
    # Create some dummy training data to initialize the model
    print("Initializing model with dummy data...")
    dummy_X = np.random.random((10, 128, 128, 3))
    dummy_y = tf.keras.utils.to_categorical(np.random.randint(0, num_classes, 10), num_classes)
    
    # Train for 1 epoch just to initialize weights
    model.fit(dummy_X, dummy_y, epochs=1, verbose=0)
    
    return model, class_names

def main():
    print("Creating demo ASL model...")
    
    # Create models directory
    os.makedirs('models', exist_ok=True)
    
    # Create the demo model
    model, class_names = create_demo_model()
    
    # Save the model
    model_path = 'models/best_asl_model.keras'
    model.save(model_path)
    print(f"Demo model saved to: {model_path}")
    
    # Also save class names
    import json
    with open('models/class_names.json', 'w') as f:
        json.dump(class_names, f)
    
    print("Demo model created successfully!")
    print("You can now run the Streamlit app with: streamlit run app.py")

if __name__ == "__main__":
    main()
