# 🤟 ASL Sign Language Detection

A comprehensive real-time American Sign Language (ASL) detection web application built with deep learning and Streamlit.

## 🌟 Features

### Machine Learning Pipeline
- **Complete CNN Architecture**: Custom-designed convolutional neural networks for ASL classification
- **Data Augmentation**: Advanced preprocessing with rotation, scaling, and brightness adjustment
- **Transfer Learning Support**: Pre-trained models (MobileNetV2, ResNet50, EfficientNetB0)
- **Model Evaluation**: Comprehensive metrics, confusion matrices, and performance analysis

### Real-time Detection
- **Live Camera Feed**: Real-time webcam integration with OpenCV
- **Hand Detection**: MediaPipe-powered hand region detection
- **Prediction Smoothing**: Multi-frame averaging for stable predictions
- **Performance Optimization**: Optimized for >10 FPS real-time processing

### Web Application
- **Interactive UI**: Clean, responsive Streamlit interface
- **Real-time Visualization**: Live prediction display with confidence scores
- **Analytics Dashboard**: Prediction history, frequency charts, and performance metrics
- **Export Functionality**: Download prediction logs and screenshots

## 🚀 Quick Start

### 1. Setup
```bash
# Clone and navigate to the project
cd "Sing Language Detector using python"

# Run setup script
python setup.py
```

### 2. Train Model
```bash
# Train the CNN model
python train_model.py

# Or use the convenience script
./run_training.sh
```

### 3. Launch Application
```bash
# Start the web application
streamlit run app.py

# Or use the convenience script
./run_app.sh
```

## 📋 Requirements

### System Requirements
- **Python**: 3.8 or higher
- **RAM**: 4GB minimum (8GB recommended)
- **Storage**: 2GB free space
- **Camera**: Webcam for real-time detection

### Dependencies
- TensorFlow 2.15.0
- Streamlit 1.28.2
- OpenCV 4.8.1
- MediaPipe 0.10.8
- NumPy, Pandas, Matplotlib, Seaborn
- Plotly, Scikit-learn, Pillow

## 📁 Project Structure

```
Sing Language Detector using python/
├── asl_dataset/                 # ASL dataset (A-Z, 0-9)
├── models/                      # Trained model files
├── logs/                        # Training logs and metrics
├── utils/                       # Utility modules
│   ├── data_preprocessing.py    # Data loading and preprocessing
│   ├── model_architecture.py    # CNN model definitions
│   ├── realtime_processing.py   # Real-time processing pipeline
│   └── evaluation.py            # Model evaluation utilities
├── train_model.py               # Model training script
├── app.py                       # Streamlit web application
├── test_application.py          # Comprehensive test suite
├── setup.py                     # Setup and installation script
├── requirements.txt             # Python dependencies
└── README.md                    # This file
```

## 🧠 Model Architecture

### Basic CNN
- 4 Convolutional blocks with BatchNormalization
- MaxPooling and Dropout for regularization
- Dense layers with 512 and 256 neurons
- Softmax output for 36 classes (A-Z, 0-9)

### Advanced CNN
- 5 Convolutional blocks with residual-like connections
- Global Average Pooling
- Enhanced regularization with varying dropout rates
- Optimized for better accuracy and generalization

### Transfer Learning
- Pre-trained backbone (MobileNetV2/ResNet50/EfficientNetB0)
- Custom classification head
- Fine-tuning capabilities

## 📊 Dataset

The application uses an ASL dataset with:
- **36 Classes**: Letters A-Z and digits 0-9
- **Multiple Variations**: Different hand positions and lighting conditions
- **Augmentation**: Rotation, scaling, brightness adjustment
- **Split**: 70% training, 15% validation, 15% testing

## 🎯 Performance Metrics

### Model Performance
- **Target Accuracy**: >85% on test set
- **Real-time Processing**: >10 FPS
- **Confidence Threshold**: Adjustable (default: 50%)
- **Prediction Smoothing**: 5-frame window

### Evaluation Metrics
- Accuracy, Precision, Recall, F1-Score
- Confusion Matrix
- Per-class performance analysis
- Confidence distribution analysis

## 🖥️ Web Application Features

### Main Interface
- **Live Camera Feed**: Real-time video with hand detection overlay
- **Current Prediction**: Large, clear display of detected sign
- **Confidence Score**: Visual confidence indicator
- **Performance Metrics**: FPS and processing time display

### Analytics Panel
- **Prediction History**: Recent predictions with timestamps
- **Frequency Charts**: Most common predictions
- **Confidence Trends**: Confidence over time
- **Session Statistics**: Duration, total predictions, averages

### Advanced Features
- **Screenshot Capture**: Automatic high-confidence prediction screenshots
- **Export Options**: Download prediction logs as CSV
- **Customizable Settings**: Confidence threshold, smoothing window
- **Theme Options**: Light/dark mode support

## 🔧 Configuration

### Training Configuration
```python
# In train_model.py
DATASET_PATH = 'asl_dataset'
IMG_SIZE = (128, 128)
MODEL_TYPE = 'advanced'  # 'basic', 'advanced', 'transfer'
EPOCHS = 50
BATCH_SIZE = 32
```

### Real-time Processing
```python
# In app.py
confidence_threshold = 0.5    # Minimum confidence for predictions
smoothing_window = 5          # Frames for prediction smoothing
img_size = (128, 128)        # Input image size
```

## 🧪 Testing

### Run Tests
```bash
# Run comprehensive test suite
python test_application.py

# Or use the convenience script
./run_tests.sh
```

### Test Coverage
- Data preprocessing utilities
- Model architecture validation
- Real-time processing pipeline
- Performance benchmarks
- Integration tests

## 🚨 Troubleshooting

### Common Issues

**Camera Not Working**
- Check camera permissions
- Ensure no other applications are using the camera
- Try different camera indices (0, 1, 2...)

**Low Performance**
- Reduce image size in configuration
- Lower batch size for training
- Close other applications to free memory

**Model Training Issues**
- Ensure sufficient disk space (>2GB)
- Check dataset integrity
- Reduce model complexity if memory issues occur

**Installation Problems**
- Update pip: `pip install --upgrade pip`
- Use virtual environment
- Check Python version compatibility

### Performance Optimization
- Use GPU for training (CUDA setup required)
- Adjust batch size based on available memory
- Enable mixed precision training for faster processing

## 📈 Future Enhancements

### Planned Features
- **Multi-hand Detection**: Support for both hands
- **Gesture Sequences**: Word and sentence recognition
- **Custom Training**: User-specific model fine-tuning
- **Mobile App**: React Native or Flutter implementation
- **Cloud Deployment**: Web-based training and inference

### Model Improvements
- **Attention Mechanisms**: Focus on important hand regions
- **Temporal Models**: LSTM/GRU for sequence recognition
- **Data Augmentation**: Advanced techniques for better generalization
- **Model Compression**: Quantization for mobile deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- ASL dataset contributors
- TensorFlow and Streamlit communities
- MediaPipe team for hand detection
- OpenCV community

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review existing issues
3. Create a new issue with detailed description
4. Include system information and error logs

## 📖 User Guide

### Getting Started

1. **Installation**
   ```bash
   python setup.py
   ```

2. **Training Your First Model**
   ```bash
   python train_model.py
   ```
   - Training will take 30-60 minutes depending on your hardware
   - Monitor progress in the terminal
   - Best model will be saved automatically

3. **Launching the Web App**
   ```bash
   streamlit run app.py
   ```
   - Open your browser to the displayed URL (usually http://localhost:8501)
   - Load a trained model from the sidebar
   - Click "Start Camera" to begin detection

### Using the Application

#### Camera Setup
- Position yourself 2-3 feet from the camera
- Ensure good lighting (avoid backlighting)
- Keep your hand clearly visible in the frame
- The green bounding box shows detected hand region

#### Making Predictions
- Form ASL signs clearly within the camera view
- Hold the sign steady for 1-2 seconds
- Watch the confidence score - higher is better
- Green text indicates high confidence (>80%)
- Yellow text indicates medium confidence (50-80%)
- Red text indicates low confidence (<50%)

#### Adjusting Settings
- **Confidence Threshold**: Minimum confidence to display predictions
- **Smoothing Window**: Number of frames to average (reduces flickering)
- **Screenshots**: Automatically save high-confidence predictions
- **Export**: Download your prediction history as CSV

### Tips for Best Results

#### Hand Positioning
- Keep your hand centered in the camera view
- Maintain consistent distance from camera
- Avoid rapid movements
- Ensure fingers are clearly visible

#### Lighting Conditions
- Use natural light when possible
- Avoid shadows on your hand
- Ensure even lighting across the hand
- Avoid bright backgrounds

#### Sign Formation
- Form signs clearly and completely
- Hold each sign for 1-2 seconds
- Practice consistent hand shapes
- Refer to ASL reference guides for proper form

---

**Made with ❤️ for the deaf and hard-of-hearing community**
