"""
Test the SimpleASLProcessor to verify it works correctly
"""

import sys
sys.path.append('.')

import numpy as np
import cv2
from app import SimpleASLProcessor

def test_processor():
    """Test the SimpleASLProcessor functionality"""
    
    print("🧪 Testing SimpleASLProcessor...")
    
    # Class names
    class_names = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'] + \
                  [chr(i) for i in range(ord('a'), ord('z') + 1)]
    
    # Initialize processor
    processor = SimpleASLProcessor('models/best_asl_model.keras', class_names)
    
    if processor.model is None:
        print("❌ Failed to load model")
        return False
    
    print("✅ Model loaded successfully")
    
    # Test with dummy frame
    test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # Test prediction
    predicted_class, confidence, all_probs = processor.predict(test_frame)
    
    if predicted_class is not None:
        print(f"✅ Prediction successful: {predicted_class} ({confidence:.3f})")
    else:
        print("❌ Prediction failed")
        return False
    
    # Test performance stats
    try:
        stats = processor.get_performance_stats()
        print(f"✅ Performance stats: {stats}")
    except Exception as e:
        print(f"❌ Performance stats failed: {e}")
        return False
    
    # Test reset
    try:
        processor.reset_history()
        print("✅ Reset history successful")
    except Exception as e:
        print(f"❌ Reset failed: {e}")
        return False
    
    print("🎉 All tests passed!")
    return True

if __name__ == "__main__":
    success = test_processor()
    if success:
        print("\n✅ SimpleASLProcessor is working correctly!")
        print("The Streamlit app should now work without errors.")
    else:
        print("\n❌ Some tests failed.")
