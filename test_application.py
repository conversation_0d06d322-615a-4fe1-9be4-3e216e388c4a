"""
Comprehensive Testing Script for ASL Sign Language Detection Application
"""

import os
import sys
import numpy as np
import cv2
import time
import unittest
from unittest.mock import Mock, patch
import tempfile
import shutil

# Add utils to path
sys.path.append('utils')

from data_preprocessing import ASLDataProcessor, preprocess_frame_for_prediction
from model_architecture import ASLCNNModel
from realtime_processing import RealTimeProcessor, FrameBuffer
from evaluation import ModelEvaluator

class TestDataPreprocessing(unittest.TestCase):
    """Test data preprocessing utilities"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_img_size = (64, 64)  # Smaller for faster testing
        self.processor = ASLDataProcessor('asl_dataset', self.test_img_size)
    
    def test_load_and_preprocess_image(self):
        """Test image loading and preprocessing"""
        # Create a test image
        test_img = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            cv2.imwrite(tmp_file.name, test_img)
            
            # Test preprocessing
            processed_img = self.processor.load_and_preprocess_image(tmp_file.name)
            
            # Assertions
            self.assertEqual(processed_img.shape, (*self.test_img_size, 3))
            self.assertTrue(0 <= processed_img.min() <= processed_img.max() <= 1)
            
            # Cleanup
            os.unlink(tmp_file.name)
    
    def test_preprocess_frame_for_prediction(self):
        """Test frame preprocessing for real-time prediction"""
        # Create test frame
        test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Test preprocessing
        processed_frame = preprocess_frame_for_prediction(test_frame, self.test_img_size)
        
        # Assertions
        self.assertEqual(processed_frame.shape, (1, *self.test_img_size, 3))
        self.assertTrue(0 <= processed_frame.min() <= processed_frame.max() <= 1)
    
    def test_prepare_labels(self):
        """Test label encoding"""
        test_labels = np.array(['a', 'b', 'c', 'a', 'b'])
        
        encoded_labels, categorical_labels, num_classes = self.processor.prepare_labels(test_labels)
        
        # Assertions
        self.assertEqual(len(encoded_labels), len(test_labels))
        self.assertEqual(categorical_labels.shape, (len(test_labels), num_classes))
        self.assertEqual(num_classes, 3)

class TestModelArchitecture(unittest.TestCase):
    """Test model architecture"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.input_shape = (64, 64, 3)
        self.num_classes = 10
        self.model_builder = ASLCNNModel(self.input_shape, self.num_classes)
    
    def test_create_basic_cnn(self):
        """Test basic CNN creation"""
        model = self.model_builder.create_basic_cnn()
        
        # Assertions
        self.assertIsNotNone(model)
        self.assertEqual(model.input_shape[1:], self.input_shape)
        self.assertEqual(model.output_shape[1], self.num_classes)
    
    def test_create_advanced_cnn(self):
        """Test advanced CNN creation"""
        model = self.model_builder.create_advanced_cnn()
        
        # Assertions
        self.assertIsNotNone(model)
        self.assertEqual(model.input_shape[1:], self.input_shape)
        self.assertEqual(model.output_shape[1], self.num_classes)
    
    def test_model_compilation(self):
        """Test model compilation"""
        model = self.model_builder.create_basic_cnn()
        compiled_model = self.model_builder.compile_model()
        
        # Assertions
        self.assertIsNotNone(compiled_model.optimizer)
        self.assertIsNotNone(compiled_model.loss)

class TestRealTimeProcessing(unittest.TestCase):
    """Test real-time processing utilities"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create a mock model
        self.mock_model = Mock()
        self.mock_model.predict.return_value = np.array([[0.1, 0.8, 0.1]])
        
        self.class_names = ['a', 'b', 'c']
        
        # Create processor with mock model
        with patch('tensorflow.keras.models.load_model', return_value=self.mock_model):
            self.processor = RealTimeProcessor(
                model_path='dummy_path',
                class_names=self.class_names,
                img_size=(64, 64)
            )
    
    def test_preprocess_frame(self):
        """Test frame preprocessing"""
        test_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        processed_frame = self.processor.preprocess_frame(test_frame)
        
        # Assertions
        self.assertIsNotNone(processed_frame)
        self.assertEqual(processed_frame.shape, (1, 64, 64, 3))
    
    def test_predict_sign(self):
        """Test sign prediction"""
        test_frame = np.random.random((1, 64, 64, 3)).astype(np.float32)
        
        predicted_class, confidence, all_probs = self.processor.predict_sign(test_frame)
        
        # Assertions
        self.assertEqual(predicted_class, 'b')  # Highest probability class
        self.assertEqual(confidence, 0.8)
        self.assertIsNotNone(all_probs)
    
    def test_smooth_predictions(self):
        """Test prediction smoothing"""
        # Add multiple predictions
        for _ in range(5):
            self.processor.smooth_predictions('a', 0.9)
        
        smoothed_class, smoothed_confidence = self.processor.smooth_predictions('b', 0.7)
        
        # Should still prefer 'a' due to history
        self.assertEqual(smoothed_class, 'a')

class TestFrameBuffer(unittest.TestCase):
    """Test frame buffer functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.buffer = FrameBuffer(max_size=5)
    
    def test_add_frame(self):
        """Test adding frames to buffer"""
        test_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        self.buffer.add_frame(test_frame)
        
        # Assertions
        self.assertEqual(len(self.buffer.frames), 1)
        self.assertEqual(len(self.buffer.timestamps), 1)
    
    def test_buffer_overflow(self):
        """Test buffer overflow handling"""
        for i in range(10):
            test_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
            self.buffer.add_frame(test_frame)
        
        # Should only keep max_size frames
        self.assertEqual(len(self.buffer.frames), 5)

class TestPerformance(unittest.TestCase):
    """Test performance and optimization"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    def test_preprocessing_speed(self):
        """Test preprocessing speed"""
        start_time = time.time()
        
        for _ in range(100):
            processed_frame = preprocess_frame_for_prediction(self.test_frame, (128, 128))
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 100
        
        # Should process frame in less than 10ms
        self.assertLess(avg_time, 0.01)
        print(f"Average preprocessing time: {avg_time*1000:.2f}ms")
    
    def test_memory_usage(self):
        """Test memory usage during processing"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Process many frames
        for _ in range(1000):
            processed_frame = preprocess_frame_for_prediction(self.test_frame, (128, 128))
            del processed_frame
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be minimal (less than 100MB)
        self.assertLess(memory_increase, 100)
        print(f"Memory increase: {memory_increase:.2f}MB")

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete pipeline"""
    
    def test_end_to_end_pipeline(self):
        """Test complete pipeline from frame to prediction"""
        # Create mock model
        mock_model = Mock()
        mock_model.predict.return_value = np.array([[0.1, 0.8, 0.1]])
        
        class_names = ['a', 'b', 'c']
        
        with patch('tensorflow.keras.models.load_model', return_value=mock_model):
            processor = RealTimeProcessor(
                model_path='dummy_path',
                class_names=class_names,
                img_size=(64, 64)
            )
        
        # Create test frame
        test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Process frame
        results = processor.process_frame(test_frame)
        
        # Assertions
        self.assertIsNotNone(results)
        self.assertIn('predicted_class', results)
        self.assertIn('confidence', results)
        self.assertIn('processing_time', results)

def run_performance_benchmark():
    """Run performance benchmark"""
    print("\n" + "="*50)
    print("PERFORMANCE BENCHMARK")
    print("="*50)
    
    # Test frame preprocessing speed
    test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # Benchmark different image sizes
    sizes = [(64, 64), (128, 128), (224, 224)]
    
    for size in sizes:
        start_time = time.time()
        
        for _ in range(100):
            processed_frame = preprocess_frame_for_prediction(test_frame, size)
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 100
        
        print(f"Size {size}: {avg_time*1000:.2f}ms per frame")
    
    print("\nTarget: <10ms per frame for real-time processing (>100 FPS)")

def run_all_tests():
    """Run all tests"""
    print("Running ASL Detection Application Tests...")
    print("="*50)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestDataPreprocessing))
    test_suite.addTest(unittest.makeSuite(TestModelArchitecture))
    test_suite.addTest(unittest.makeSuite(TestRealTimeProcessing))
    test_suite.addTest(unittest.makeSuite(TestFrameBuffer))
    test_suite.addTest(unittest.makeSuite(TestPerformance))
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Run performance benchmark
    run_performance_benchmark()
    
    # Print summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
