"""
Real-time ASL Sign Language Detection Web Application
Built with Streamlit
"""

import streamlit as st
import cv2
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import time
import os
import sys
from collections import deque
from datetime import datetime, timedelta
import json
from PIL import Image
import base64
import io

# Add utils to path
sys.path.append('utils')

from realtime_processing import RealTimeProcessor, FrameBuffer
from data_preprocessing import ASLDataProcessor

# Page configuration
st.set_page_config(
    page_title="ASL Sign Language Detector",
    page_icon="🤟",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .prediction-box {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 10px;
        border-left: 5px solid #1f77b4;
        margin: 1rem 0;
    }
    .confidence-high {
        color: #28a745;
        font-weight: bold;
    }
    .confidence-medium {
        color: #ffc107;
        font-weight: bold;
    }
    .confidence-low {
        color: #dc3545;
        font-weight: bold;
    }
    .metric-card {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)

class ASLStreamlitApp:
    def __init__(self):
        """Initialize the Streamlit ASL Detection App"""
        self.processor = None
        self.class_names = self.get_class_names()
        self.prediction_history = deque(maxlen=50)
        self.confidence_history = deque(maxlen=50)
        self.frame_buffer = FrameBuffer(max_size=10)
        
        # Initialize session state
        if 'camera_active' not in st.session_state:
            st.session_state.camera_active = False
        if 'model_loaded' not in st.session_state:
            st.session_state.model_loaded = False
        if 'prediction_count' not in st.session_state:
            st.session_state.prediction_count = 0
        if 'saved_screenshots' not in st.session_state:
            st.session_state.saved_screenshots = []
        if 'session_start_time' not in st.session_state:
            st.session_state.session_start_time = datetime.now()
        if 'total_processing_time' not in st.session_state:
            st.session_state.total_processing_time = 0.0
    
    def get_class_names(self):
        """Get class names from dataset or default list"""
        # Default ASL classes (A-Z, 0-9)
        letters = [chr(i) for i in range(ord('a'), ord('z') + 1)]
        numbers = [str(i) for i in range(10)]
        return sorted(letters + numbers)
    
    def load_model(self, model_path):
        """Load the trained model"""
        try:
            if os.path.exists(model_path):
                self.processor = RealTimeProcessor(
                    model_path=model_path,
                    class_names=self.class_names,
                    confidence_threshold=st.session_state.get('confidence_threshold', 0.5)
                )
                st.session_state.model_loaded = True
                return True
            else:
                st.error(f"Model file not found: {model_path}")
                return False
        except Exception as e:
            st.error(f"Error loading model: {e}")
            return False
    
    def render_header(self):
        """Render the application header"""
        st.markdown('<h1 class="main-header">🤟 ASL Sign Language Detector</h1>', 
                   unsafe_allow_html=True)
        st.markdown("""
        <div style="text-align: center; margin-bottom: 2rem;">
            <p style="font-size: 1.2rem; color: #666;">
                Real-time American Sign Language detection using deep learning
            </p>
        </div>
        """, unsafe_allow_html=True)
    
    def render_sidebar(self):
        """Render the sidebar with controls"""
        st.sidebar.header("🎛️ Controls")
        
        # Model selection
        st.sidebar.subheader("Model Settings")
        model_options = []
        
        if os.path.exists('models/best_asl_model.keras'):
            model_options.append('models/best_asl_model.keras')
        if os.path.exists('models/asl_model_final.keras'):
            model_options.append('models/asl_model_final.keras')
        
        if model_options:
            selected_model = st.sidebar.selectbox(
                "Select Model",
                model_options,
                index=0
            )
            
            if st.sidebar.button("Load Model"):
                with st.spinner("Loading model..."):
                    if self.load_model(selected_model):
                        st.sidebar.success("Model loaded successfully!")
                    else:
                        st.sidebar.error("Failed to load model")
        else:
            st.sidebar.warning("No trained models found. Please train a model first.")
            if st.sidebar.button("Train Model"):
                st.info("Please run `python train_model.py` to train a model first.")
        
        # Camera controls
        st.sidebar.subheader("Camera Controls")
        
        if st.sidebar.button("Start Camera" if not st.session_state.camera_active else "Stop Camera"):
            st.session_state.camera_active = not st.session_state.camera_active
        
        # Detection settings
        st.sidebar.subheader("Detection Settings")
        
        confidence_threshold = st.sidebar.slider(
            "Confidence Threshold",
            min_value=0.0,
            max_value=1.0,
            value=0.5,
            step=0.05,
            help="Minimum confidence for displaying predictions"
        )
        
        smoothing_window = st.sidebar.slider(
            "Smoothing Window",
            min_value=1,
            max_value=10,
            value=5,
            help="Number of frames for prediction smoothing"
        )
        
        # Update processor settings
        if self.processor:
            self.processor.confidence_threshold = confidence_threshold
            self.processor.smoothing_window = smoothing_window
        
        # Performance settings
        st.sidebar.subheader("Performance")
        show_fps = st.sidebar.checkbox("Show FPS", value=True)
        show_processing_time = st.sidebar.checkbox("Show Processing Time", value=True)

        # Advanced features
        st.sidebar.subheader("Advanced Features")
        enable_screenshots = st.sidebar.checkbox("Enable Screenshots", value=True)
        show_probability_distribution = st.sidebar.checkbox("Show Probability Distribution", value=True)
        enable_prediction_logging = st.sidebar.checkbox("Enable Prediction Logging", value=True)

        # Export options
        st.sidebar.subheader("Export Options")
        if st.sidebar.button("Export Prediction Log"):
            self.export_prediction_log()

        if st.sidebar.button("Clear History"):
            self.clear_history()

        # Theme selection
        st.sidebar.subheader("Appearance")
        theme = st.sidebar.selectbox("Theme", ["Light", "Dark"], index=0)

        return {
            'confidence_threshold': confidence_threshold,
            'smoothing_window': smoothing_window,
            'show_fps': show_fps,
            'show_processing_time': show_processing_time,
            'enable_screenshots': enable_screenshots,
            'show_probability_distribution': show_probability_distribution,
            'enable_prediction_logging': enable_prediction_logging,
            'theme': theme
        }
    
    def render_main_interface(self, settings):
        """Render the main interface"""
        if not st.session_state.model_loaded:
            st.warning("Please load a model from the sidebar to start detection.")
            return
        
        # Create columns for layout
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.subheader("📹 Live Camera Feed")
            
            # Placeholder for video feed
            video_placeholder = st.empty()
            
            if st.session_state.camera_active:
                self.run_camera_detection(video_placeholder, settings)
            else:
                # Show placeholder image
                placeholder_img = np.zeros((480, 640, 3), dtype=np.uint8)
                cv2.putText(placeholder_img, "Camera Off", (200, 240), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
                video_placeholder.image(placeholder_img, channels="BGR", use_column_width=True)
        
        with col2:
            self.render_prediction_panel(settings)

        # Additional sections
        if settings.get('show_probability_distribution', True) and len(self.prediction_history) > 0:
            st.subheader("📊 Probability Distribution")
            # This will be updated in real-time during camera processing

        # Session statistics
        self.render_session_statistics()

        # Saved screenshots
        if settings.get('enable_screenshots', True):
            self.render_saved_screenshots()
    
    def run_camera_detection(self, video_placeholder, settings):
        """Run real-time camera detection"""
        if not self.processor:
            st.error("Processor not initialized")
            return
        
        # Initialize camera
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            st.error("Could not open camera")
            return
        
        # Set camera properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        try:
            while st.session_state.camera_active:
                ret, frame = cap.read()
                
                if not ret:
                    st.error("Failed to read from camera")
                    break
                
                # Process frame
                results = self.processor.process_frame(frame)
                
                # Draw results on frame
                output_frame = self.processor.draw_results(frame, results)
                
                # Update prediction history
                if results['smoothed_class'] and results['smoothed_confidence'] > settings['confidence_threshold']:
                    self.prediction_history.append(results['smoothed_class'])
                    self.confidence_history.append(results['smoothed_confidence'])
                    st.session_state.prediction_count += 1

                    # Save screenshot if enabled and high confidence
                    if settings.get('enable_screenshots', True) and results['smoothed_confidence'] > 0.8:
                        self.save_screenshot(frame, results['smoothed_class'], results['smoothed_confidence'])
                
                # Display frame
                video_placeholder.image(output_frame, channels="BGR", use_column_width=True)
                
                # Small delay to prevent overwhelming the interface
                time.sleep(0.03)  # ~30 FPS
                
        except Exception as e:
            st.error(f"Error during detection: {e}")
        finally:
            cap.release()
    
    def render_prediction_panel(self, settings):
        """Render the prediction results panel"""
        st.subheader("🎯 Predictions")
        
        # Current prediction
        if len(self.prediction_history) > 0:
            current_prediction = self.prediction_history[-1]
            current_confidence = self.confidence_history[-1]
            
            # Determine confidence level styling
            if current_confidence >= 0.8:
                confidence_class = "confidence-high"
            elif current_confidence >= 0.5:
                confidence_class = "confidence-medium"
            else:
                confidence_class = "confidence-low"
            
            st.markdown(f"""
            <div class="prediction-box">
                <h3>Current Prediction</h3>
                <h2 style="color: #1f77b4; margin: 0;">{current_prediction.upper()}</h2>
                <p class="{confidence_class}">Confidence: {current_confidence:.1%}</p>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown("""
            <div class="prediction-box">
                <h3>Current Prediction</h3>
                <p style="color: #666;">No predictions yet</p>
            </div>
            """, unsafe_allow_html=True)
        
        # Prediction history
        if len(self.prediction_history) > 0:
            st.subheader("📊 Recent Predictions")
            
            # Show last 10 predictions
            recent_predictions = list(self.prediction_history)[-10:]
            recent_confidences = list(self.confidence_history)[-10:]
            
            history_df = pd.DataFrame({
                'Sign': recent_predictions,
                'Confidence': recent_confidences
            })
            
            st.dataframe(history_df, use_container_width=True)
            
            # Prediction frequency chart
            if len(self.prediction_history) >= 5:
                self.render_prediction_charts()
        
        # Statistics
        self.render_statistics()
    
    def render_prediction_charts(self):
        """Render prediction visualization charts"""
        st.subheader("📈 Analytics")
        
        # Prediction frequency
        prediction_counts = pd.Series(list(self.prediction_history)).value_counts()
        
        fig_freq = px.bar(
            x=prediction_counts.index,
            y=prediction_counts.values,
            title="Prediction Frequency",
            labels={'x': 'Sign', 'y': 'Count'}
        )
        fig_freq.update_layout(height=300)
        st.plotly_chart(fig_freq, use_container_width=True)
        
        # Confidence over time
        if len(self.confidence_history) >= 5:
            fig_conf = px.line(
                y=list(self.confidence_history)[-20:],
                title="Confidence Over Time",
                labels={'y': 'Confidence', 'index': 'Frame'}
            )
            fig_conf.update_layout(height=300)
            st.plotly_chart(fig_conf, use_container_width=True)
    
    def render_statistics(self):
        """Render performance statistics"""
        st.subheader("📊 Statistics")
        
        # Performance metrics
        if self.processor:
            stats = self.processor.get_performance_stats()
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.metric("Average FPS", f"{stats['avg_fps']:.1f}")
                st.metric("Total Predictions", st.session_state.prediction_count)
            
            with col2:
                st.metric("Avg Processing Time", f"{stats['avg_processing_time']:.1f}ms")
                if len(self.confidence_history) > 0:
                    avg_confidence = np.mean(list(self.confidence_history))
                    st.metric("Average Confidence", f"{avg_confidence:.1%}")
    
    def export_prediction_log(self):
        """Export prediction history to CSV"""
        if len(self.prediction_history) == 0:
            st.warning("No predictions to export")
            return

        # Create DataFrame
        df = pd.DataFrame({
            'Timestamp': [datetime.now() - timedelta(seconds=i) for i in range(len(self.prediction_history)-1, -1, -1)],
            'Prediction': list(self.prediction_history),
            'Confidence': list(self.confidence_history)
        })

        # Convert to CSV
        csv = df.to_csv(index=False)

        # Create download button
        st.sidebar.download_button(
            label="Download CSV",
            data=csv,
            file_name=f"asl_predictions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )
        st.sidebar.success("Prediction log ready for download!")

    def clear_history(self):
        """Clear all prediction history"""
        self.prediction_history.clear()
        self.confidence_history.clear()
        st.session_state.prediction_count = 0
        st.session_state.saved_screenshots = []
        st.session_state.session_start_time = datetime.now()
        if self.processor:
            self.processor.reset_history()
        st.sidebar.success("History cleared!")

    def save_screenshot(self, frame, prediction, confidence):
        """Save screenshot with prediction"""
        try:
            # Convert frame to PIL Image
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            img = Image.fromarray(frame_rgb)

            # Save to buffer
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            buffer.seek(0)

            # Encode to base64
            img_str = base64.b64encode(buffer.getvalue()).decode()

            # Store screenshot data
            screenshot_data = {
                'timestamp': datetime.now(),
                'prediction': prediction,
                'confidence': confidence,
                'image_data': img_str
            }

            st.session_state.saved_screenshots.append(screenshot_data)

            # Limit number of saved screenshots
            if len(st.session_state.saved_screenshots) > 20:
                st.session_state.saved_screenshots.pop(0)

        except Exception as e:
            st.error(f"Error saving screenshot: {e}")

    def render_probability_distribution(self, probabilities):
        """Render probability distribution chart"""
        if probabilities is None:
            return

        # Get top 10 predictions
        top_indices = np.argsort(probabilities)[-10:][::-1]
        top_classes = [self.class_names[i] for i in top_indices]
        top_probs = probabilities[top_indices]

        # Create horizontal bar chart
        fig = go.Figure(go.Bar(
            x=top_probs,
            y=top_classes,
            orientation='h',
            marker_color='lightblue'
        ))

        fig.update_layout(
            title="Top 10 Prediction Probabilities",
            xaxis_title="Probability",
            yaxis_title="Sign",
            height=400,
            margin=dict(l=50, r=50, t=50, b=50)
        )

        st.plotly_chart(fig, use_container_width=True)

    def render_session_statistics(self):
        """Render session statistics"""
        st.subheader("📊 Session Statistics")

        # Calculate session duration
        session_duration = datetime.now() - st.session_state.session_start_time

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Session Duration", f"{session_duration.seconds // 60}m {session_duration.seconds % 60}s")

        with col2:
            st.metric("Total Predictions", st.session_state.prediction_count)

        with col3:
            if st.session_state.prediction_count > 0:
                predictions_per_minute = st.session_state.prediction_count / (session_duration.seconds / 60) if session_duration.seconds > 0 else 0
                st.metric("Predictions/Min", f"{predictions_per_minute:.1f}")
            else:
                st.metric("Predictions/Min", "0.0")

        with col4:
            if len(self.confidence_history) > 0:
                avg_confidence = np.mean(list(self.confidence_history))
                st.metric("Avg Confidence", f"{avg_confidence:.1%}")
            else:
                st.metric("Avg Confidence", "N/A")

    def render_saved_screenshots(self):
        """Render saved screenshots gallery"""
        if len(st.session_state.saved_screenshots) == 0:
            return

        st.subheader("📸 Saved Screenshots")

        # Display screenshots in a grid
        cols = st.columns(3)

        for i, screenshot in enumerate(st.session_state.saved_screenshots[-9:]):  # Show last 9
            col_idx = i % 3

            with cols[col_idx]:
                # Decode image
                img_data = base64.b64decode(screenshot['image_data'])
                img = Image.open(io.BytesIO(img_data))

                st.image(img, caption=f"{screenshot['prediction']} ({screenshot['confidence']:.1%})", use_column_width=True)
                st.caption(screenshot['timestamp'].strftime("%H:%M:%S"))

    def run(self):
        """Run the Streamlit application"""
        self.render_header()
        settings = self.render_sidebar()
        self.render_main_interface(settings)

def main():
    """Main function to run the Streamlit app"""
    app = ASLStreamlitApp()
    app.run()

if __name__ == "__main__":
    main()
