"""
Setup and Installation Script for ASL Sign Language Detection Application
"""

import os
import sys
import subprocess
import platform
import pkg_resources
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def check_system_requirements():
    """Check system requirements"""
    print("\n🔍 Checking system requirements...")
    
    # Check operating system
    os_name = platform.system()
    print(f"Operating System: {os_name}")
    
    # Check available memory
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        print(f"Available RAM: {memory_gb:.1f} GB")
        
        if memory_gb < 4:
            print("⚠️  Warning: Less than 4GB RAM available. Training may be slow.")
    except ImportError:
        print("Could not check memory (psutil not installed)")
    
    # Check for GPU
    try:
        import tensorflow as tf
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"✅ GPU detected: {len(gpus)} device(s)")
        else:
            print("ℹ️  No GPU detected. Using CPU for training.")
    except ImportError:
        print("TensorFlow not installed yet")
    
    return True

def install_requirements():
    """Install required packages"""
    print("\n📦 Installing requirements...")
    
    requirements_file = "requirements.txt"
    
    if not os.path.exists(requirements_file):
        print(f"❌ {requirements_file} not found")
        return False
    
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install requirements
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_file])
        
        print("✅ Requirements installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False

def verify_installation():
    """Verify that all packages are installed correctly"""
    print("\n🔍 Verifying installation...")
    
    required_packages = [
        'tensorflow',
        'streamlit',
        'opencv-python',
        'numpy',
        'pandas',
        'matplotlib',
        'seaborn',
        'scikit-learn',
        'plotly',
        'mediapipe',
        'Pillow'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            pkg_resources.get_distribution(package)
            print(f"✅ {package}")
        except pkg_resources.DistributionNotFound:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        return False
    
    print("\n✅ All packages installed successfully")
    return True

def setup_directories():
    """Create necessary directories"""
    print("\n📁 Setting up directories...")
    
    directories = [
        "models",
        "logs",
        "utils",
        "data",
        "exports"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created/verified: {directory}/")
    
    return True

def check_dataset():
    """Check if dataset is available"""
    print("\n📊 Checking dataset...")
    
    dataset_path = "asl_dataset"
    
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset not found at {dataset_path}")
        print("Please ensure the ASL dataset is available in the project directory")
        return False
    
    # Count classes and images
    try:
        class_dirs = [d for d in os.listdir(dataset_path) 
                     if os.path.isdir(os.path.join(dataset_path, d)) and d != 'asl_dataset']
        
        total_images = 0
        for class_dir in class_dirs:
            class_path = os.path.join(dataset_path, class_dir)
            images = [f for f in os.listdir(class_path) 
                     if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            total_images += len(images)
        
        print(f"✅ Dataset found: {len(class_dirs)} classes, {total_images} images")
        return True
        
    except Exception as e:
        print(f"❌ Error checking dataset: {e}")
        return False

def test_camera():
    """Test camera access"""
    print("\n📹 Testing camera access...")
    
    try:
        import cv2
        
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ Could not access camera")
            print("Please check camera permissions and connections")
            return False
        
        ret, frame = cap.read()
        cap.release()
        
        if ret:
            print("✅ Camera access successful")
            return True
        else:
            print("❌ Could not read from camera")
            return False
            
    except Exception as e:
        print(f"❌ Error testing camera: {e}")
        return False

def create_run_scripts():
    """Create convenient run scripts"""
    print("\n📝 Creating run scripts...")
    
    # Training script
    train_script = """#!/bin/bash
echo "Starting ASL Model Training..."
python train_model.py
"""
    
    # App script
    app_script = """#!/bin/bash
echo "Starting ASL Detection Web App..."
streamlit run app.py
"""
    
    # Test script
    test_script = """#!/bin/bash
echo "Running Application Tests..."
python test_application.py
"""
    
    scripts = {
        "run_training.sh": train_script,
        "run_app.sh": app_script,
        "run_tests.sh": test_script
    }
    
    for script_name, script_content in scripts.items():
        with open(script_name, 'w') as f:
            f.write(script_content)
        
        # Make executable on Unix systems
        if platform.system() != 'Windows':
            os.chmod(script_name, 0o755)
        
        print(f"✅ Created: {script_name}")
    
    return True

def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETE!")
    print("="*60)
    
    print("\n📋 Next Steps:")
    print("1. Train the model:")
    print("   python train_model.py")
    print("   (or run: ./run_training.sh)")
    
    print("\n2. Start the web application:")
    print("   streamlit run app.py")
    print("   (or run: ./run_app.sh)")
    
    print("\n3. Run tests (optional):")
    print("   python test_application.py")
    print("   (or run: ./run_tests.sh)")
    
    print("\n📚 Documentation:")
    print("- Check README.md for detailed instructions")
    print("- Model files will be saved in models/ directory")
    print("- Training logs will be saved in logs/ directory")
    
    print("\n🔧 Troubleshooting:")
    print("- If camera doesn't work, check permissions")
    print("- For GPU issues, ensure CUDA is properly installed")
    print("- For memory issues, reduce batch size in training")
    
    print("\n" + "="*60)

def main():
    """Main setup function"""
    print("🚀 ASL Sign Language Detection - Setup Script")
    print("="*60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check system requirements
    if not check_system_requirements():
        print("⚠️  System requirements check failed, but continuing...")
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements")
        sys.exit(1)
    
    # Verify installation
    if not verify_installation():
        print("❌ Installation verification failed")
        sys.exit(1)
    
    # Setup directories
    if not setup_directories():
        print("❌ Failed to setup directories")
        sys.exit(1)
    
    # Check dataset
    if not check_dataset():
        print("⚠️  Dataset check failed, but continuing...")
    
    # Test camera
    if not test_camera():
        print("⚠️  Camera test failed, but continuing...")
    
    # Create run scripts
    if not create_run_scripts():
        print("⚠️  Failed to create run scripts, but continuing...")
    
    # Print next steps
    print_next_steps()

if __name__ == "__main__":
    main()
